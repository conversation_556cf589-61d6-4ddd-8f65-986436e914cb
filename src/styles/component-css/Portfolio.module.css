.portfolioSection {
  padding: var(--space-32)0;
  position: relative;
  overflow: hidden;
  background-color: var(--bg-dark)
}

.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden
}

.backgroundGradient {
  position: absolute;
  top: -10%;
  left: -10%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle at 80% 30%, rgba(0, 240, 181, .08), transparent 60%), radial-gradient(circle at 20% 70%, rgba(61, 158, 238, .08), transparent 60%), radial-gradient(circle at 50% 50%, rgba(157, 78, 255, .05), transparent 70%);
  filter: blur(100px);
  opacity: .8;
  z-index: 0;
  transform-style: preserve-3d;
  animation: gradientShift 15s ease-in-out infinite alternate
}

.backgroundGrid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to right, rgba(255, 255, 255, .02) 1px, transparent 1px), linear-gradient(to bottom, rgba(255, 255, 255, .02) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: .4;
  z-index: 0
}

.portfolioHeading {
  text-align: center;
  margin-bottom: var(--space-16);
  position: relative;
  z-index: 1
}

.sectionLabel {
  display: inline-block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: .1em;
  color: var(--primary);
  background-color: rgba(0, 240, 181, .08);
  padding: .5rem 1rem;
  border-radius: 50px;
  margin-bottom: var(--space-4)
}

.portfolioTitle {
  font-size: var(--text-5xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  background-clip: text;
  color: transparent;
  letter-spacing: -.02em
}

.portfolioSubtitle {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto var(--space-10);
  line-height: var(--leading-relaxed)
}

.filterContainer {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin: var(--space-8)0
}

.filterButton {
  padding: .75rem 1.5rem;
  border-radius: 8px;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all .3s ease;
  background-color: rgba(255, 255, 255, .05);
  color: var(--text-secondary);
  border: 1px solid rgba(255, 255, 255, .1)
}

.portfolioGrid {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
  width: 100%;
  position: relative;
  z-index: 1;
}

.projectCard {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: row;
  background: rgba(15, 17, 24, .5);
  border: 1px solid rgba(255, 255, 255, .05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, .1);
  aspect-ratio: auto;
}

.projectCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, .2), 0 0 15px rgba(0, 240, 181, .1);
  border-color: rgba(255, 255, 255, .1)
}

.projectImageWrapper {
  flex: 2;
  position: relative;
}

.projectImageContainer {
  position: relative;
  width: calc(100% - 40px);
  height: calc(100% - 40px);
  overflow: hidden;
  background-color: rgba(0, 0, 0, .2);
  /* contain: layout size; */
  margin: 20px;
  border-radius: 10px;
  aspect-ratio: 3/2;
}

.projectImage {
  /* position: absolute; */
  top: 0;
  left: 0;
  width: 100% !important;
  height:  auto;
  position: relative;
  /* height: 100% !important; */
  /* object-fit: cover; */
  /* transition: transform .5s cubic-bezier(.34, 1.56, .64, 1); */
  /* will-change: transform; */
  /* transform: translateZ(0) scale(1); */
  /* backface-visibility: hidden; */
  /* contain: paint */
}

.projectCard:hover .projectImage {
  /* filter: blur(10px) */
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity .3s ease
}

.projectCard:hover .imageOverlay {
  opacity: 1
}

.viewProjectButton {
  /* padding: .75rem 1.5rem; */
  /* background: linear-gradient(135deg, var(--primary), var(--primary-dark)); */
  color: var(--primary);
  /* border-radius: 8px; */
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  transition: .2s ease;;
  display: block;
  margin-top: 1rem;
}

.projectCard:hover .viewProjectButton {
  /* transform: translateY(0);
  opacity: 1 */
}

.viewProjectButton:hover {
  /* transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, .2) */
}

.projectContent {
  flex: 1;
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  background: rgba(15, 17, 24, .8);
}

.projectLogo {
  margin-top: var(--space-4);
  margin-bottom: var(--space-8);
  display: inline-block;
}

.logoImage {
  max-height: 38px;
  width: auto;
  max-width: 100%;
  display: block;
}

.projectTitle {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  transition: color .3s ease;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.projectCard:hover .projectTitle {
  color: var(--primary)
}

.projectDescription {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
  flex: 1
}

.projectTechStack {
  margin-top: auto
}

.techBar {
  display: flex;
  height: 6px;
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-3);
  background-color: rgba(255, 255, 255, .05);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, .2)
}

.techBarSegment {
  height: 100%;
  transition: width .5s ease
}

.jsColor {
  background-color: #f0db4f
}

.htmlColor {
  background-color: #e44d26
}

.cssColor {
  background-color: #2965f1
}

.scssColor {
  background-color: #cd6799
}

.vueColor {
  background-color: #42b883
}
.nodeColor {
  background-color: #b84264
}
.postgresColor {
  background-color: #6742b8
}

.phpColor {
  background-color: #8892be
}

.webflowColor {
  background-color: #4263eb
}

.nextJSColor {
  background-color: #fff
}

.techList {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  list-style: none;
  padding: 0;
  margin: 0
}

.techItem {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background-color: rgba(255, 255, 255, .05);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  display: inline-flex;
  align-items: center;
  transition: all .3s ease
}

.techItem:hover {
  background-color: rgba(255, 255, 255, .1);
  color: var(--text-primary)
}

.techColor {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-1)
}

.projectStatus {
  /* position: absolute; */
  /* top: 12px; */
  /* right: 12px; */
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  background-color: rgba(0, 0, 0, .5);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  z-index: 2;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, .2)
}

.projectStatus.live {
  color: var(--primary);
  background-color: rgba(0, 0, 0, .7);
  border: 1px solid rgba(0, 240, 181, .3)
}

.ctaContainer {
  display: flex;
  justify-content: center;
  margin-top: var(--space-16);
  position: relative;
  z-index: 1
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: .75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: var(--bg-dark);
  font-weight: var(--font-semibold);
  border-radius: 8px;
  transition: all .3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, .2)
}

.ctaButton::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  opacity: 0;
  transition: opacity .3s ease;
  z-index: -1
}

.ctaButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, .3), 0 0 20px rgba(0, 240, 181, .2)
}

.ctaButton:hover::before {
  opacity: 1
}

@media (max-width:1200px) {
  .portfolioGrid {
    /* grid-template-columns: repeat(2, 1fr); */
    gap: var(--space-6)
  }

  .portfolioTitle {
    font-size: var(--text-4xl)
  }

  .portfolioSubtitle {
    font-size: var(--text-lg);
    max-width: 600px
  }
}

@media (max-width:1024px) {
  .portfolioSection {
    padding: var(--space-24)0
  }

  .portfolioGrid {
    /* grid-template-columns: repeat(2, 1fr); */
    gap: var(--space-5)
  }

  .projectContent {
    padding: var(--space-5)
  }

  .filterContainer {
    flex-wrap: wrap
  }
}

@media (max-width:768px) {
  .portfolioSection {
    padding: var(--space-20)0
  }

  .projectCard{
    flex-direction: column;
  }

  .portfolioTitle {
    font-size: var(--text-3xl)
  }

  .portfolioSubtitle {
    font-size: var(--text-base);
    margin-bottom: var(--space-6)
  }

  .sectionLabel {
    font-size: var(--text-xs);
    padding: .4rem .8rem
  }

  .portfolioGrid {
    grid-template-columns: 1fr;
    gap: var(--space-6)
  }

  .projectContent {
    padding: var(--space-4)
  }

  .projectTitle {
    font-size: var(--text-lg)
  }

  .projectDescription {
    font-size: var(--text-xs)
  }

  .techItem {
    font-size: calc(var(--text-xs) - 1px)
  }

  .filterButton {
    padding: .6rem 1.2rem;
    font-size: var(--text-xs)
  }

  .ctaContainer {
    margin-top: var(--space-12)
  }

  .ctaButton {
    padding: .85rem 1.75rem;
    font-size: var(--text-sm)
  }
}

@media (max-width:480px) {
  .portfolioSection {
    padding: var(--space-16)0
  }

  .portfolioTitle {
    font-size: var(--text-2xl)
  }

  .portfolioSubtitle {
    font-size: var(--text-sm)
  }

  .filterContainer {
    gap: var(--space-2);
    margin: var(--space-6)0
  }

  .filterButton {
    padding: .5rem 1rem;
    font-size: calc(var(--text-xs) - 1px)
  }

  .projectImageContainer {
    padding-bottom: 66.67%;
  min-height: 200px;

  }

  .viewProjectButton {
    padding: .6rem 1.2rem;
    font-size: var(--text-xs)
  }

  .projectContent {
    padding: var(--space-3)
  }

  .projectTitle {
    font-size: var(--text-base)
  }

  .projectDescription {
    font-size: calc(var(--text-xs) - 1px);
    margin-bottom: var(--space-3)
  }

  .techBar {
    height: 4px;
    margin-bottom: var(--space-2)
  }

  .techList {
    gap: var(--space-1)
  }

  .techItem {
    padding: .15rem .5rem;
    font-size: calc(var(--text-xs) - 2px)
  }

  .techColor {
    width: 6px;
    height: 6px
  }

  .ctaButton {
    padding: .75rem 1.5rem;
    font-size: var(--text-xs)
  }
}